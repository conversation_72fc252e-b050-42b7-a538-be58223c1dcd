<% content_for :title, "Dynamic Bundles" %>

<div class="d-flex justify-content-between align-items-center mb-3">
  <h2>Dynamic Bundles</h2>
  <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBundleModal">
    Create New Bundle
  </button>
</div>

<% if @total_count > 0 %>
  <p class="text-muted">
    Showing <%= @bundles.length %> of <%= @total_count %> bundles
    <% if @pagination[:current_page] && @pagination[:total_pages] %>
      (Page <%= @pagination[:current_page] %> of <%= @pagination[:total_pages] %>)
    <% end %>
  </p>

  <table class="table">
    <thead>
      <tr>
        <th>Name</th>
        <th>SKU</th>
        <th>Description</th>
        <th>Status</th>
        <th>Categories</th>
        <th>Created</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <% @bundles.each do |bundle| %>
        <tr>
          <td>
            <strong><%= bundle['name'] %></strong>
          </td>
          <td>
            <code><%= bundle['sku'] %></code>
          </td>
          <td>
            <%= truncate(bundle['description'], length: 60) %>
          </td>
          <td>
            <% if bundle['status'] == 'active' %>
              <span class="badge badge-success">Active</span>
            <% else %>
              <span class="badge badge-secondary">Inactive</span>
            <% end %>
          </td>
          <td>
            <% categories_count = bundle.dig('metadata', 'categories')&.length || 0 %>
            <%= categories_count %> categories
          </td>
          <td>
            <% if bundle['created_at'] %>
              <%= time_ago_in_words(Time.parse(bundle['created_at'])) %> ago
            <% else %>
              -
            <% end %>
          </td>
          <td>
            <div class="action-icons">
              <%= link_to admin_bundle_path(bundle['id']),
                  class: "action-icon view-icon",
                  title: "View bundle details" do %>
                👁️
              <% end %>

              <%= link_to edit_admin_bundle_path(bundle['id']),
                  class: "action-icon edit-icon",
                  title: "Edit bundle" do %>
                ✏️
              <% end %>

              <% if bundle['status'] == 'active' %>
                <%= link_to toggle_status_admin_bundle_path(bundle['id']),
                    method: :patch,
                    class: "action-icon deactivate-icon",
                    title: "Deactivate bundle",
                    data: {
                      confirm: "Are you sure you want to deactivate this bundle?",
                      turbo_method: :patch
                    } do %>
                  ⏸️
                <% end %>
              <% else %>
                <%= link_to toggle_status_admin_bundle_path(bundle['id']),
                    method: :patch,
                    class: "action-icon activate-icon",
                    title: "Activate bundle",
                    data: { turbo_method: :patch } do %>
                  ▶️
                <% end %>
              <% end %>

              <%= link_to admin_bundle_path(bundle['id']),
                  method: :delete,
                  class: "action-icon delete-icon",
                  title: "Delete bundle",
                  data: {
                    confirm: "Are you sure you want to delete this bundle? This action cannot be undone.",
                    turbo_method: :delete
                  } do %>
                🗑️
              <% end %>
            </div>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>

  <!-- Pagination -->
  <% if @pagination[:total_pages] && @pagination[:total_pages] > 1 %>
    <nav class="mt-4">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <% if @pagination[:current_page] > 1 %>
            <%= link_to "← Previous", admin_bundles_path(page: @pagination[:current_page] - 1), 
                class: "btn btn-secondary" %>
          <% end %>
        </div>
        
        <div>
          Page <%= @pagination[:current_page] %> of <%= @pagination[:total_pages] %>
        </div>
        
        <div>
          <% if @pagination[:current_page] < @pagination[:total_pages] %>
            <%= link_to "Next →", admin_bundles_path(page: @pagination[:current_page] + 1), 
                class: "btn btn-secondary" %>
          <% end %>
        </div>
      </div>
    </nav>
  <% end %>

<% else %>
  <!-- Empty State -->
  <div class="text-center" style="padding: 60px 20px;">
    <div style="font-size: 48px; color: #ccc; margin-bottom: 20px;">📦</div>
    <h3>No Dynamic Bundles Yet</h3>
    <p class="text-muted mb-4">
      Create your first dynamic bundle to get started with configurable product bundles.
    </p>
    <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#createBundleModal">
      Create Your First Bundle
    </button>
  </div>
<% end %>

<% content_for :modals do %>
<!-- Bundle Creation Modal -->
<div class="modal fade" id="createBundleModal" tabindex="-1" aria-labelledby="createBundleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="createBundleModalLabel">
          <span class="me-2">📦</span>Create New Dynamic Bundle
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>

      <%= form_with url: admin_bundles_path, method: :post, local: true, id: "createBundleForm", class: "needs-validation", novalidate: true, scope: :bundle do |form| %>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-8">
              <div class="mb-3">
                <%= form.label :name, "Bundle Name", class: "form-label" %>
                <%= form.text_field :name, class: "form-control", placeholder: "e.g., Yoli Transformation Bundle", required: true %>
                <div class="invalid-feedback">
                  Please provide a bundle name.
                </div>
                <div class="form-text">
                  This will be the display name for your bundle
                </div>
              </div>

              <div class="mb-3">
                <%= form.label :sku, "SKU", class: "form-label" %>
                <%= form.text_field :sku, class: "form-control", placeholder: "e.g., YOLI-TRANS-001", required: true %>
                <div class="invalid-feedback">
                  Please provide a unique SKU.
                </div>
                <div class="form-text">
                  Unique identifier for this bundle
                </div>
              </div>

              <div class="mb-3">
                <%= form.label :description, "Description", class: "form-label" %>
                <%= form.text_area :description, class: "form-control", rows: 3, placeholder: "Describe what this bundle offers..." %>
                <div class="form-text">
                  Optional description for customers
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="bundle-preview-card">
                <h6 class="mb-3">Bundle Preview</h6>
                <div class="preview-content">
                  <div class="preview-name">Bundle Name</div>
                  <div class="preview-sku">SKU</div>
                  <div class="preview-description">Description will appear here...</div>
                  <div class="preview-categories">
                    <small class="text-muted">Categories: 0</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">
            Continue to Builder <span class="ms-1">→</span>
          </button>
        </div>
      <% end %>
    </div>
  </div>
</div>
<% end %>

<style>
  .badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 4px;
  }
  
  .badge-success {
    background-color: #d4edda;
    color: #155724;
  }
  
  .badge-secondary {
    background-color: #e2e3e5;
    color: #383d41;
  }
  
  .badge-warning {
    background-color: #fff3cd;
    color: #856404;
  }
  
  .action-icons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-start;
  }

  .action-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 16px;
    transition: all 0.2s ease;
    cursor: pointer;
    border: 1px solid transparent;
  }

  .action-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .view-icon {
    background-color: #f8f9fa;
    border-color: #e9ecef;
  }

  .view-icon:hover {
    background-color: #e9ecef;
    border-color: #6c757d;
  }

  .edit-icon {
    background-color: #e3f2fd;
    border-color: #bbdefb;
  }

  .edit-icon:hover {
    background-color: #bbdefb;
    border-color: #2196f3;
  }

  .activate-icon {
    background-color: #e8f5e8;
    border-color: #c8e6c9;
  }

  .activate-icon:hover {
    background-color: #c8e6c9;
    border-color: #4caf50;
  }

  .deactivate-icon {
    background-color: #fff3e0;
    border-color: #ffcc02;
  }

  .deactivate-icon:hover {
    background-color: #ffcc02;
    border-color: #ff9800;
  }

  .delete-icon {
    background-color: #ffebee;
    border-color: #ffcdd2;
  }

  .delete-icon:hover {
    background-color: #ffcdd2;
    border-color: #f44336;
  }
  
  code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
  }

  /* Bundle Creation Modal Styles */
  .bundle-preview-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    height: fit-content;
  }

  .preview-content {
    font-size: 14px;
  }

  .preview-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
    background-color: #fff3cd;
    padding: 4px 8px;
    border-radius: 4px;
    border: 2px solid #ffc107;
  }

  .preview-sku {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #6c757d;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
    margin-bottom: 8px;
  }

  .preview-description {
    color: #6c757d;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 12px;
    min-height: 40px;
  }

  .preview-categories {
    border-top: 1px solid #e9ecef;
    padding-top: 8px;
  }

  .modal-header {
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .modal-header .btn-close {
    filter: invert(1);
  }

  .modal-title {
    font-weight: 600;
  }

  .form-label {
    font-weight: 500;
    color: #495057;
  }

  .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Ensure Bootstrap is loaded
  if (typeof bootstrap === 'undefined') {
    console.warn('Bootstrap not loaded, using fallback modal functionality');
  }

  // Bundle Creation Modal - Live Preview
  const modal = document.getElementById('createBundleModal');
  const nameInput = document.querySelector('#createBundleForm input[name="bundle[name]"]');
  const skuInput = document.querySelector('#createBundleForm input[name="bundle[sku]"]');
  const descriptionInput = document.querySelector('#createBundleForm textarea[name="bundle[description]"]');

  const previewName = document.querySelector('.preview-name');
  const previewSku = document.querySelector('.preview-sku');
  const previewDescription = document.querySelector('.preview-description');

  console.log('Elements found:', {
    modal: !!modal,
    nameInput: !!nameInput,
    skuInput: !!skuInput,
    descriptionInput: !!descriptionInput,
    previewName: !!previewName,
    previewSku: !!previewSku,
    previewDescription: !!previewDescription
  });

  function updatePreview() {
    console.log('updatePreview called');
    if (nameInput && previewName) {
      console.log('Updating name:', nameInput.value);
      console.log('previewName element:', previewName);
      previewName.textContent = nameInput.value || 'Bundle Name';
      console.log('previewName after update:', previewName.textContent);
    } else {
      console.log('Missing elements for name update:', { nameInput: !!nameInput, previewName: !!previewName });
    }
    if (skuInput && previewSku) {
      console.log('Updating SKU:', skuInput.value);
      previewSku.textContent = skuInput.value || 'SKU';
    }
    if (descriptionInput && previewDescription) {
      console.log('Updating description:', descriptionInput.value);
      previewDescription.textContent = descriptionInput.value || 'Description will appear here...';
    }
  }

  // Auto-generate SKU from name
  function generateSku(name) {
    return name
      .toUpperCase()
      .replace(/[^A-Z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 20);
  }

  if (nameInput) {
    nameInput.addEventListener('input', function() {
      updatePreview();

      // Auto-generate SKU if it's empty
      if (skuInput && !skuInput.value) {
        const generatedSku = generateSku(this.value);
        if (generatedSku) {
          skuInput.value = generatedSku + '-001';
          updatePreview();
        }
      }
    });
  }

  if (skuInput) {
    skuInput.addEventListener('input', updatePreview);
  }

  if (descriptionInput) {
    descriptionInput.addEventListener('input', updatePreview);
  }

  // Reset form when modal is closed
  if (modal) {
    modal.addEventListener('hidden.bs.modal', function() {
      const form = document.getElementById('createBundleForm');
      if (form) {
        form.reset();
        form.classList.remove('was-validated');
        updatePreview();
      }
    });
  }

  // Form validation
  const form = document.getElementById('createBundleForm');
  if (form) {
    form.addEventListener('submit', function(event) {
      if (!form.checkValidity()) {
        event.preventDefault();
        event.stopPropagation();
      }
      form.classList.add('was-validated');
    });
  }

  // Handle delete confirmations with better UX
  const deleteLinks = document.querySelectorAll('a[data-turbo-method="delete"]');
  
  deleteLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      const bundleName = this.closest('tr').querySelector('strong').textContent;
      const confirmMessage = `Are you sure you want to delete "${bundleName}"?\n\nThis action cannot be undone and will remove all associated categories and products.`;
      
      if (!confirm(confirmMessage)) {
        e.preventDefault();
        return false;
      }
    });
  });
  
  // Handle status toggle confirmations
  const statusLinks = document.querySelectorAll('a[data-turbo-method="patch"]');
  
  statusLinks.forEach(link => {
    if (link.textContent.includes('Deactivate')) {
      link.addEventListener('click', function(e) {
        const bundleName = this.closest('tr').querySelector('strong').textContent;
        const confirmMessage = `Are you sure you want to deactivate "${bundleName}"?\n\nThis will make the bundle unavailable for customers.`;
        
        if (!confirm(confirmMessage)) {
          e.preventDefault();
          return false;
        }
      });
    }
  });
});
</script>
